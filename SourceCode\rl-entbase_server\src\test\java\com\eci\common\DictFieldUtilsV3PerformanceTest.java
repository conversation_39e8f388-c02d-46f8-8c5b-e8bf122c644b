package com.eci.common;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * DictFieldUtilsV3 性能测试类
 * 验证优化后的缓存机制和性能提升
 */
@SpringBootTest
@ActiveProfiles("test")
public class DictFieldUtilsV3PerformanceTest {

    @Autowired
    private DictFieldUtilsV3 dictFieldUtils;

    @BeforeEach
    void setUp() {
        // 确保缓存预热完成
        if (!dictFieldUtils.isPreheated()) {
            dictFieldUtils.triggerPreheat();
            try {
                Thread.sleep(2000); // 等待预热完成
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    @Test
    @DisplayName("测试单个实体的字典字段处理性能")
    void testSingleEntityPerformance() {
        System.out.println("=== 单个实体性能测试 ===");
        
        // 创建测试实体
        TestEntity entity = createTestEntity();
        
        // 第一次调用（可能需要加载缓存）
        long startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entity);
        long firstCallDuration = System.currentTimeMillis() - startTime;
        
        // 第二次调用（应该使用缓存）
        startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entity);
        long secondCallDuration = System.currentTimeMillis() - startTime;
        
        // 第三次调用（验证缓存稳定性）
        startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entity);
        long thirdCallDuration = System.currentTimeMillis() - startTime;
        
        System.out.printf("第一次调用耗时: %d ms%n", firstCallDuration);
        System.out.printf("第二次调用耗时: %d ms%n", secondCallDuration);
        System.out.printf("第三次调用耗时: %d ms%n", thirdCallDuration);
        
        // 验证缓存效果：第二次和第三次调用应该明显快于第一次
        assert secondCallDuration < firstCallDuration : "第二次调用应该比第一次快（缓存效果）";
        assert thirdCallDuration < firstCallDuration : "第三次调用应该比第一次快（缓存效果）";
        
        System.out.println("✓ 单个实体性能测试通过");
    }

    @Test
    @DisplayName("测试批量实体的字典字段处理性能")
    void testBatchEntityPerformance() {
        System.out.println("=== 批量实体性能测试 ===");
        
        // 创建测试实体列表
        List<TestEntity> entities = createTestEntities(100);
        
        // 第一次批量处理
        long startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entities);
        long firstBatchDuration = System.currentTimeMillis() - startTime;
        
        // 第二次批量处理（应该使用缓存）
        startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entities);
        long secondBatchDuration = System.currentTimeMillis() - startTime;
        
        System.out.printf("第一次批量处理耗时: %d ms (100个实体)%n", firstBatchDuration);
        System.out.printf("第二次批量处理耗时: %d ms (100个实体)%n", secondBatchDuration);
        System.out.printf("第一次平均耗时: %.2f ms/个%n", (double) firstBatchDuration / entities.size());
        System.out.printf("第二次平均耗时: %.2f ms/个%n", (double) secondBatchDuration / entities.size());
        
        // 验证批量处理的缓存效果
        assert secondBatchDuration < firstBatchDuration : "第二次批量处理应该比第一次快";
        
        System.out.println("✓ 批量实体性能测试通过");
    }

    @Test
    @DisplayName("测试缓存预热效果")
    void testCachePreheatingEffect() {
        System.out.println("=== 缓存预热效果测试 ===");
        
        // 清空缓存
        dictFieldUtils.clearCache();
        
        // 创建测试实体
        TestEntity entity = createTestEntity();
        
        // 未预热情况下的处理时间
        long startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entity);
        long withoutPreheatDuration = System.currentTimeMillis() - startTime;
        
        // 触发预热
        dictFieldUtils.triggerPreheat();
        try {
            Thread.sleep(2000); // 等待预热完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 预热后的处理时间
        startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entity);
        long withPreheatDuration = System.currentTimeMillis() - startTime;
        
        System.out.printf("未预热处理耗时: %d ms%n", withoutPreheatDuration);
        System.out.printf("预热后处理耗时: %d ms%n", withPreheatDuration);
        System.out.printf("性能提升: %.1f%%n", 
            ((double)(withoutPreheatDuration - withPreheatDuration) / withoutPreheatDuration) * 100);
        
        // 验证预热效果
        assert withPreheatDuration <= withoutPreheatDuration : "预热后的处理时间应该不超过未预热时间";
        
        System.out.println("✓ 缓存预热效果测试通过");
    }

    @Test
    @DisplayName("测试缓存统计信息")
    void testCacheStatistics() {
        System.out.println("=== 缓存统计信息测试 ===");
        
        // 处理一些实体以产生缓存数据
        List<TestEntity> entities = createTestEntities(10);
        dictFieldUtils.handleDictFields(entities);
        
        // 获取缓存统计信息
        String stats = dictFieldUtils.getCacheStats();
        System.out.println(stats);
        
        // 验证统计信息包含必要的内容
        assert stats.contains("缓存统计") : "统计信息应包含标题";
        assert stats.contains("预热状态") : "统计信息应包含预热状态";
        assert stats.contains("缓存大小") : "统计信息应包含缓存大小信息";
        
        System.out.println("✓ 缓存统计信息测试通过");
    }

    @Test
    @DisplayName("测试并发处理性能")
    void testConcurrentPerformance() {
        System.out.println("=== 并发处理性能测试 ===");
        
        List<TestEntity> entities = createTestEntities(50);
        
        // 串行处理
        long startTime = System.currentTimeMillis();
        for (TestEntity entity : entities) {
            dictFieldUtils.handleDictFields(entity);
        }
        long serialDuration = System.currentTimeMillis() - startTime;
        
        // 并行处理（使用批量方法）
        startTime = System.currentTimeMillis();
        dictFieldUtils.handleDictFields(entities);
        long parallelDuration = System.currentTimeMillis() - startTime;
        
        System.out.printf("串行处理耗时: %d ms%n", serialDuration);
        System.out.printf("并行处理耗时: %d ms%n", parallelDuration);
        System.out.printf("性能提升: %.1f%%n", 
            ((double)(serialDuration - parallelDuration) / serialDuration) * 100);
        
        // 并行处理应该更快（在多核环境下）
        System.out.println("✓ 并发处理性能测试完成");
    }

    /**
     * 创建测试实体
     */
    private TestEntity createTestEntity() {
        TestEntity entity = new TestEntity();
        entity.setStatus("Y");
        entity.setStage("DISPATCH");
        entity.setConsigneeCode("test01");
        entity.setOpType("TRANSPORT");
        entity.setCustomerType("NORMAL");
        entity.setProductCode("PROD001");
        return entity;
    }

    /**
     * 创建测试实体列表
     */
    private List<TestEntity> createTestEntities(int count) {
        List<TestEntity> entities = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            TestEntity entity = createTestEntity();
            entity.setId((long) i);
            entities.add(entity);
        }
        return entities;
    }

    /**
     * 测试实体类
     */
    public static class TestEntity extends ZsrBaseEntity {
        private Long id;
        
        @DictField(queryKey = "STATUS", suffix = "Name")
        private String status;
        
        @DictField(queryKey = "STAGE", suffix = "Name")
        private String stage;
        
        @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A WHERE A.STATUS = 'Y'", suffix = "Name")
        private String consigneeCode;
        
        @DictField(sql = "SELECT A.CODE, A.NAME FROM FZGJ_BD_OP_TYPE A WHERE A.STATUS = 'Y'", suffix = "Name")
        private String opType;
        
        @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y'", suffix = "Name")
        private String customerType;
        
        @DictField(sql = "SELECT A.CODE, A.NAME FROM FZGJ_BD_PRODUCT A WHERE A.STATUS = 'Y'", suffix = "Name")
        private String productCode;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getStage() { return stage; }
        public void setStage(String stage) { this.stage = stage; }
        
        public String getConsigneeCode() { return consigneeCode; }
        public void setConsigneeCode(String consigneeCode) { this.consigneeCode = consigneeCode; }
        
        public String getOpType() { return opType; }
        public void setOpType(String opType) { this.opType = opType; }
        
        public String getCustomerType() { return customerType; }
        public void setCustomerType(String customerType) { this.customerType = customerType; }
        
        public String getProductCode() { return productCode; }
        public void setProductCode(String productCode) { this.productCode = productCode; }
    }
}
